using System;
using System.Globalization;
using System.Windows.Data;

namespace DriverManagementSystem.Converters
{
    /// <summary>
    /// محول لعرض عدد أيام المشروع - يظهر "غير محدد" عندما تكون القيمة 0
    /// </summary>
    public class ProjectDaysConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is int days)
            {
                if (days <= 0)
                {
                    return "غير محدد";
                }
                return days.ToString();
            }
            
            return "غير محدد";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string text)
            {
                if (text == "غير محدد")
                {
                    return 0;
                }
                
                if (int.TryParse(text, out int result))
                {
                    return result;
                }
            }
            
            return 0;
        }
    }
}
