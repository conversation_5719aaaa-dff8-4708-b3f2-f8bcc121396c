using System;
using System.Globalization;
using System.Windows.Data;

namespace DriverManagementSystem.Converters
{
    /// <summary>
    /// محول لعرض وحدة "يوم" فقط عندما تكون هناك قيمة حقيقية لعدد الأيام
    /// </summary>
    public class ProjectDaysUnitConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is int days && days > 0)
            {
                return "يوم";
            }
            
            return string.Empty; // لا تظهر "يوم" إذا كانت القيمة 0 أو غير محددة
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
